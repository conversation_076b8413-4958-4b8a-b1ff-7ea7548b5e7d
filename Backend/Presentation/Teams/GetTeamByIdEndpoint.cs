using Application.Teams.Dtos;
using Application.Teams.Queries;
using MediatR;
using Presentation.Abstractions;
using Presentation.Extensions;
using Shared;

namespace Presentation.Teams;

public class GetTeamByIdEndpoint : IEndpoint
{
    public void Map(RouteGroupBuilder routeGroupBuilder) =>
        routeGroupBuilder.MapGet("/{id:guid}", Handle);

    private static async Task<IResult> Handle(
        Guid id,
        ISender sender,
        CancellationToken cancellationToken
    )
    {
        Result<TeamDto> result = await sender.Send(new GetTeamByIdQuery(id), cancellationToken);
        if (result.IsFailure)
        {
            return result.Error.ToProblemDetails();
        }

        return Results.Ok(result.Value);
    }
}
