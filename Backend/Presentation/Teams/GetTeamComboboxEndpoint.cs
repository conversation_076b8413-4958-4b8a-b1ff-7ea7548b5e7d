using Application.Shared;
using Application.Teams.Queries;
using MediatR;
using Presentation.Abstractions;
using Presentation.Extensions;
using Shared;

namespace Presentation.Teams;

public class GetTeamComboboxEndpoint : IEndpoint
{
    public void Map(RouteGroupBuilder routeGroupBuilder) => routeGroupBuilder.MapGet("/combobox", Handle);

    private static async Task<IResult> Handle(
        [AsParameters] GetTeamComboboxQuery request,
        ISender sender,
        CancellationToken cancellationToken
    )
    {
        Result<PaginatedList<ComboboxItem>> result = await sender.Send(request, cancellationToken);
        if (result.IsFailure)
        {
            return result.Error.ToProblemDetails();
        }
        return Results.Ok(result.Value);
    }
}
