using Application.Teams.Commands;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Presentation.Abstractions;
using Presentation.Extensions;
using Shared;

namespace Presentation.Teams;

public class AddTeamMemberEndpoint : IEndpoint
{
    public void Map(RouteGroupBuilder routeGroupBuilder) => routeGroupBuilder.MapPost("/{id:guid}/member", HandleAsync);

    private static async Task<IResult> HandleAsync(
        Guid id,
        [FromBody] List<Guid> userIds,
        ISender sender,
        CancellationToken cancellationToken
    )
    {
        var result = await sender.Send(new AddTeamMemberCommand(id, userIds), cancellationToken);
        if (result.IsFailure)
        {
            return result.Error.ToProblemDetails();
        }

        return Results.Ok();
    }
}
