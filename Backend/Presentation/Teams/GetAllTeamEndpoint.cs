using Application.Shared;
using Application.Teams.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Presentation.Abstractions;
using Presentation.Extensions;

namespace Presentation.Teams;

public class GetAllTeamEndpoint : IEndpoint
{
    public void Map(RouteGroupBuilder routeGroupBuilder) =>
        routeGroupBuilder.MapGet("/", HandleAsync);

    private static async Task<IResult> HandleAsync(
        ISender sender,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] GetListSort? sort = null,
        [FromQuery] GetListFilter[]? filter = null,
        CancellationToken cancellationToken = default
    )
    {
        var request = new GetAllTeamQuery(
            page,
            pageSize,
            sort,
            filter?.ToList() ?? new List<GetListFilter>()
        );
        var result = await sender.Send(request, cancellationToken);
        if (result.IsFailure)
        {
            return result.Error.ToProblemDetails();
        }

        return Results.Ok(result.Value);
    }
}
