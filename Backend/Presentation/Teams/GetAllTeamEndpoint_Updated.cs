using Application.Shared;
using Application.Teams.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Presentation.Abstractions;
using Presentation.Extensions;

namespace Presentation.Teams;

public class GetAllTeamEndpoint : IEndpoint
{
    public void Map(RouteGroupBuilder routeGroupBuilder) =>
        routeGroupBuilder.MapGet("/", HandleAsync);

    private static async Task<IResult> HandleAsync(
        ISender sender,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 10,
        [FromQuery] string? sortField = null,
        [FromQuery] string? sortOrder = "asc",
        [FromQuery] string[]? filterField = null,
        [FromQuery] string[]? filterValue = null,
        CancellationToken cancellationToken = default
    )
    {
        // Parse sort parameter
        GetListSort? sort = null;
        if (!string.IsNullOrWhiteSpace(sortField))
        {
            sort = new GetListSort { Field = sortField, Order = sortOrder ?? "asc" };
        }

        // Parse filter parameters - support multiple filters
        var filters = new List<GetListFilter>();
        if (filterField != null && filterValue != null)
        {
            var maxLength = Math.Min(filterField.Length, filterValue.Length);
            for (int i = 0; i < maxLength; i++)
            {
                if (!string.IsNullOrWhiteSpace(filterField[i]) && !string.IsNullOrWhiteSpace(filterValue[i]))
                {
                    filters.Add(new GetListFilter { Field = filterField[i], Value = filterValue[i] });
                }
            }
        }

        var request = new GetAllTeamQuery(
            page,
            pageSize,
            sort,
            filters
        );
        var result = await sender.Send(request, cancellationToken);
        if (result.IsFailure)
        {
            return result.Error.ToProblemDetails();
        }

        return Results.Ok(result.Value);
    }
}
