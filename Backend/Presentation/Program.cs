using System.Globalization;
using Application.Extensions;
using Infrastructure.Extensions;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Tokens;
using Presentation.Extensions;
using Presentation.Helper;
using Presentation.Middlewares;
using Scalar.AspNetCore;
using Serilog;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);
ConfigurationManager configuration = builder.Configuration;

builder.Services.AddHttpContextAccessor();
builder.Services.AddApplication();
builder.Services.AddInfrastructure(configuration);
builder.Services.AddOpenApi();

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .WriteTo.Console(formatProvider: CultureInfo.InvariantCulture)
    .CreateLogger();

builder.Services.AddSerilog(
    (services, config) =>
        config
            .ReadFrom.Configuration(configuration)
            .ReadFrom.Services(services)
            .Enrich.FromLogContext()
            .Enrich.WithCorrelationId(),
    true
);

EnableCorsAttribute cors = new("AllowAll");
builder.Services.AddCors(options =>
{
    options.AddPolicy(
        cors.PolicyName!,
        policy =>
        {
            policy
                .AllowAnyMethod()
                .AllowAnyHeader()
                .AllowCredentials()
                .SetIsOriginAllowed(_ => true);
        }
    );
});

builder
    .Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.RequireHttpsMetadata = false;
        options.Authority = configuration["Keycloak:Authority"];
        options.Audience = configuration["Keycloak:Audience"];
        options.IncludeErrorDetails = true;
        options.MetadataAddress =
            configuration["Keycloak:Authority"] + "/.well-known/openid-configuration";
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = configuration["Keycloak:Authority"],
            ValidateAudience = true,
            ValidAudience = configuration["Keycloak:Audience"],
            ValidateLifetime = true,
            ValidateIssuerSigningKey = false,
            SignatureValidator = (token, parameters) => new JsonWebToken(token),
            ClockSkew = TimeSpan.Zero,
            // RequireExpirationTime = true,
        };
        options.Events = new JwtBearerEvents
        {
            OnTokenValidated = context =>
            {
                KeycloakHelper.MapKeycloakRolesToRoleClaims(context);
                return Task.CompletedTask;
            },
            OnAuthenticationFailed = context =>
            {
                // Console.WriteLine($"Error: {context.Exception.Message}");
                return Task.CompletedTask;
            },
        };
    });

builder
    .Services.AddAuthorizationBuilder()
    .SetDefaultPolicy(
        new AuthorizationPolicyBuilder()
            .RequireAuthenticatedUser()
            // .RequireClaim("email_verified", "true")
            .Build()
    );

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();
builder.Services.AddProblemDetails();

builder.Services.AddEndpoints();

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

WebApplication app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.ApplyMigrations();
    app.MapScalarApiReference(options =>
    {
        const string scalarEndpoint = "http://localhost:5544";
        options.Title = "Etheris Api";
        options.Theme = ScalarTheme.DeepSpace;
        options.Servers = [new ScalarServer(scalarEndpoint, "Development")];
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.UseMiddleware<RequestLogContextMiddleware>();

app.UseExceptionHandler();

app.MapEndpoints();

app.Run();
