using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Teams.Dtos;
using Application.Teams.Queries;
using Domain.Teams;
using Microsoft.EntityFrameworkCore;
using Shared;

namespace Application.Teams.QueryHandlers;

public class GetTeamByIdQueryHandler : IQueryHandler<GetTeamByIdQuery, TeamDto>
{
    private readonly IApplicationDbContext _dbContext;

    public GetTeamByIdQueryHandler(IApplicationDbContext dbContext) => _dbContext = dbContext;

    public async Task<Result<TeamDto>> Handle(
        GetTeamByIdQuery request,
        CancellationToken cancellationToken
    )
    {
        Team? team = await _dbContext.Teams.FirstOrDefaultAsync(
            x => x.Id == request.Id,
            cancellationToken
        );

        if (team is null)
        {
            return Result.Failure<TeamDto>(
                Error.NotFound("team.notfound", $"Team with ID {request.Id} was not found.")
            );
        }

        return new TeamDto(team.Id, team.Name);
    }
}
