using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Shared;
using Application.Teams.Queries;
using Domain.Teams;
using Microsoft.EntityFrameworkCore;
using Shared;

namespace Application.Teams.Dtos.QueryHandlers;

public class GetAllTeamQueryHandler : IQueryHandler<GetAllTeamQuery, PaginatedList<TeamDto>>
{
    private readonly IApplicationDbContext _dbContext;

    public GetAllTeamQueryHandler(IApplicationDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<Result<PaginatedList<TeamDto>>> Handle(
        GetAllTeamQuery request,
        CancellationToken cancellationToken
    )
    {
        var query = _dbContext.Teams.AsNoTracking();
        if (request.Filters is not null)
        {
            query = Filter(query, request.Filters);
        }
        if (request.Sort is not null)
        {
            query = Sort(query, request.Sort);
        }
        var queryDto = query.Select(x => new TeamDto(x.Id, x.Name));
        return await PaginatedList<TeamDto>.CreateAsync(queryDto, request.Page, request.PageSize);
    }

    private static IQueryable<Team> Filter(IQueryable<Team> query, IList<GetListFilter> filter)
    {
        foreach (var item in filter)
        {
            if (item.Value is null)
            {
                continue;
            }
            query = item.Field switch
            {
                "name" => query.Where(x => x.Name.Contains(item.Value)),
                _ => query,
            };
        }
        return query;
    }

    private static IQueryable<Team> Sort(IQueryable<Team> query, GetListSort sort)
    {
        return sort.Field switch
        {
            "name" => sort.Order == "asc"
                ? query.OrderBy(x => x.Name)
                : query.OrderByDescending(x => x.Name),
            _ => query,
        };
    }
}
