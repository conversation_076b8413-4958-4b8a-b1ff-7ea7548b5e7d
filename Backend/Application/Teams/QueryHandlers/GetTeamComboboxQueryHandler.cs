using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Shared;
using Application.Teams.Queries;
using Domain.Teams;
using Shared;

namespace Application.Teams.QueryHandlers;

public class GetTeamComboboxQueryHandler : IQueryHandler<GetTeamComboboxQuery, PaginatedList<ComboboxItem>>
{
    private readonly IApplicationDbContext _dbContext;

    public GetTeamComboboxQueryHandler(IApplicationDbContext dbContext) => _dbContext = dbContext;

    public async Task<Result<PaginatedList<ComboboxItem>>> Handle(GetTeamComboboxQuery request,
        CancellationToken cancellationToken)
    {
        IQueryable<Team> query = string.IsNullOrWhiteSpace(request.SearchTerm)
            ? _dbContext.Teams
            : _dbContext.Teams.Where(x => x.Name.Contains(request.SearchTerm));

        return await PaginatedList<ComboboxItem>.CreateAsync(
            query.Select(x => new ComboboxItem(x.Name, x.Id.ToString())),
            request.Page,
            request.PageSize
        );
    }
}
