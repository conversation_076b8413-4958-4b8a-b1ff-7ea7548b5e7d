using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Teams.Commands;
using Domain.Teams;
using Shared;

namespace Application.Teams.Dtos.CommandHandlers;

public sealed class DeleteTeamCommandHandler : ICommandHandler<DeleteTeamCommand, Guid>
{
    private readonly ITeamRepository _teamRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DeleteTeamCommandHandler(ITeamRepository teamRepository, IUnitOfWork unitOfWork)
    {
        _teamRepository = teamRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(
        DeleteTeamCommand request,
        CancellationToken cancellationToken
    )
    {
        Team? team = await _teamRepository.GetByIdAsync(request.Id);
        if (team is null)
        {
            return Result.Failure<Guid>(Error.NotFound("team.notfound", "Team not found"));
        }

        _teamRepository.Delete(team);

        int count = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (count == 0)
        {
            return Result.Failure<Guid>(Error.Failure("team.delete", "Failed to delete team"));
        }

        return team.Id;
    }
}
