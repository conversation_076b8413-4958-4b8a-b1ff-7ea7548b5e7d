using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Teams.Commands;
using Domain.Teams;
using Shared;

namespace Application.Teams.Dtos.CommandHandlers;

public class CreateTeamCommandHandler : ICommandHandler<CreateTeamCommand, Guid>
{
    private readonly ITeamRepository _teamRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateTeamCommandHandler(ITeamRepository teamRepository, IUnitOfWork unitOfWork)
    {
        _teamRepository = teamRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(
        CreateTeamCommand request,
        CancellationToken cancellationToken
    )
    {
        Guid id = Guid.CreateVersion7();
        Team team = new(id, request.Name);
        _teamRepository.Add(team);
        int count = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (count == 0)
        {
            return Result.Failure<Guid>(Error.Failure("team.create", "Failed to create team"));
        }

        return id;
    }
}
