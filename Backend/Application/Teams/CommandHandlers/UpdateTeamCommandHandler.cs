using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Teams.Commands;
using Domain.Teams;
using Shared;

namespace Application.Teams.Dtos.CommandHandlers;

public sealed class UpdateTeamCommandHandler : ICommandHandler<UpdateTeamCommand, Guid>
{
    private readonly ITeamRepository _teamRepository;
    private readonly IUnitOfWork _unitOfWork;

    public UpdateTeamCommandHandler(ITeamRepository teamRepository, IUnitOfWork unitOfWork)
    {
        _teamRepository = teamRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result<Guid>> Handle(
        UpdateTeamCommand request,
        CancellationToken cancellationToken
    )
    {
        Team? team = await _teamRepository.GetByIdAsync(request.Id);
        if (team is null)
        {
            return Result.Failure<Guid>(Error.NotFound("team.notfound", "Team not found"));
        }

        team.SetName(request.Name);
        _teamRepository.Update(team);

        int count = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (count == 0)
        {
            return Result.Failure<Guid>(Error.Failure("team.update", "Failed to update team"));
        }

        return team.Id;
    }
}
