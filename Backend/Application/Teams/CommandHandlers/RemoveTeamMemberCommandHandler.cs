using Application.Abstractions.Data;
using Application.Abstractions.Messaging;
using Application.Teams.Commands;
using Domain.Teams;
using Shared;

namespace Application.Teams.Dtos.CommandHandlers;

public class RemoveTeamMemberCommandHandler : ICommandHandler<RemoveTeamMemberCommand>
{
    private readonly ITeamRepository _teamRepository;
    private readonly IUnitOfWork _unitOfWork;

    public RemoveTeamMemberCommandHandler(ITeamRepository teamRepository, IUnitOfWork unitOfWork)
    {
        _teamRepository = teamRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Result> Handle(RemoveTeamMemberCommand request, CancellationToken cancellationToken)
    {
        var team = await _teamRepository.GetByIdAsync(request.TeamId, includeMembers: true);
        if (team is null)
        {
            return Result.Failure(Error.NotFound("team.notfound", "Team not found"));
        }

        var members = request.MemberIds.Select(x => new TeamMember(request.TeamId, x)).ToList();
        team.RemoveMemberRange(members);
        _teamRepository.RemoveMemberRange(members);
        _teamRepository.Update(team);

        int count = await _unitOfWork.SaveChangesAsync(cancellationToken);
        if (count == 0)
        {
            return Result.Failure(Error.Failure("team.update", "Failed to update team"));
        }

        return Result.Success();
    }
}
