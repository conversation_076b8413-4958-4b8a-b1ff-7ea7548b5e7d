using System.Diagnostics.CodeAnalysis;
using System.Web;
using Newtonsoft.Json;

namespace Application.Shared;

public record GetListRequest(
    int Page = 1,
    int PageSize = 10,
    GetListSort? Sort = null,
    List<GetListFilter>? Filters = null
);

public class GetListSort
{
    public string? Field { get; set; }
    public string? Order { get; set; } = "asc";

    public static bool TryParse(
        [NotNullWhen(true)] string? s,
        IFormatProvider? provider,
        [Maybe<PERSON>ull<PERSON>hen(false)] out GetListSort result
    )
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            result = null;
            return false;
        }
        try
        {
            var decodedString = System.Web.HttpUtility.UrlDecode(s);
            result = JsonConvert.DeserializeObject<GetListSort>(decodedString)!;
            return true;
        }
        catch
        {
            result = null;
            return false;
        }
    }
}

public class GetListFilter
{
    public string Field { get; set; } = null!;
    public string Value { get; set; } = null!;

    public static bool TryParse(
        [NotN<PERSON><PERSON><PERSON>(true)] string? s,
        IFormatProvider? provider,
        [<PERSON><PERSON><PERSON><PERSON><PERSON>(false)] out GetListFilter result
    )
    {
        if (string.IsNullOrWhiteSpace(s))
        {
            result = null;
            return false;
        }
        try
        {
            result = JsonConvert.DeserializeObject<GetListFilter>(s)!;
            return true;
        }
        catch
        {
            result = null;
            return false;
        }
    }
}
