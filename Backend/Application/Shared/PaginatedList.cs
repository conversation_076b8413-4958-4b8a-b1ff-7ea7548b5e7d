using Microsoft.EntityFrameworkCore;

namespace Application.Shared;

public sealed class PaginatedList<T>
{
    private PaginatedList() { }

    public int Page { get; set; }
    public int PageSize { get; set; }
    public int TotalCount { get; set; }
    public List<T> Items { get; set; } = [];
    public bool HasNextPage => Page * PageSize < TotalCount;
    public bool HasPreviousPage => Page > 1 && PageSize > 0 && Page < TotalCount / PageSize;

    public static async Task<PaginatedList<T>> CreateAsync(
        IQueryable<T> source,
        int page,
        int pageSize
    )
    {
        if (page < 1 || pageSize < 1)
        {
            throw new ArgumentException("Page and page size must be greater than 0");
        }
        return new()
        {
            Page = page,
            PageSize = pageSize,
            TotalCount = await source.CountAsync().ConfigureAwait(true),
            Items = await source
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync()
                .ConfigureAwait(true),
        };
    }
}
